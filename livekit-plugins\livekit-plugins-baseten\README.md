# Baseten plugin for LiveKit Agents

Support for [Baseten](https://baseten.co/)-hosted models in LiveKit Agents.

More information is available in the docs for the [TTS](https://docs.livekit.io/agents/integrations/tts/baseten/) and [STT](https://docs.livekit.io/agents/integrations/stt/baseten/) integrations.

## Installation

```bash
pip install livekit-plugins-baseten
```

## Pre-requisites

You'll need an API key from Baseten. It can be set as an environment variable: `BASETEN_API_KEY`

You also need to deploy a model to Baseten and will need your model endpoint to configure the plugin.
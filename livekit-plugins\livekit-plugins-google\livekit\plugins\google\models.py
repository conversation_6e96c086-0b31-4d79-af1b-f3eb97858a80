from typing import Literal

# Speech to Text v2

SpeechModels = Literal[
    "long",
    "short",
    "telephony",
    "medical_dictation",
    "medical_conversation",
    "chirp",
    "chirp_2",
    "latest_long",
    "latest_short",
]

SpeechLanguages = Literal[
    "af-ZA",
    "am-ET",
    "ar-AE",
    "ar-BH",
    "ar-DZ",
    "ar-EG",
    "ar-IL",
    "ar-IQ",
    "ar-JO",
    "ar-KW",
    "ar-LB",
    "ar-MA",
    "ar-MR",
    "ar-OM",
    "ar-PS",
    "ar-QA",
    "ar-SA",
    "ar-TN",
    "ar-YE",
    "as-IN",
    "ast-ES",
    "az-AZ",
    "be-BY",
    "bg-BG",
    "bn-BD",
    "bn-IN",
    "bs-BA",
    "ca-ES",
    "ceb-PH",
    "ckb-IQ",
    "cmn-Hans-CN",
    "cmn-Hant-TW",
    "cs-CZ",
    "cy-GB",
    "da-DK",
    "de-AT",
    "de-CH",
    "de-DE",
    "el-GR",
    "en-AU",
    "en-CA",
    "en-GB",
    "en-HK",
    "en-IE",
    "en-IN",
    "en-NZ",
    "en-PK",
    "en-SG",
    "en-US",
    "es-419",
    "es-AR",
    "es-BO",
    "es-CL",
    "es-CO",
    "es-CR",
    "es-DO",
    "es-EC",
    "es-ES",
    "es-GT",
    "es-HN",
    "es-MX",
    "es-NI",
    "es-PA",
    "es-PE",
    "es-PR",
    "es-SV",
    "es-US",
    "es-UY",
    "es-VE",
    "et-EE",
    "eu-ES",
    "fa-IR",
    "ff-SN",
    "fi-FI",
    "fil-PH",
    "fr-BE",
    "fr-CA",
    "fr-CH",
    "fr-FR",
    "ga-IE",
    "gl-ES",
    "gu-IN",
    "ha-NG",
    "hi-IN",
    "hr-HR",
    "hu-HU",
    "hy-AM",
    "id-ID",
    "ig-NG",
    "is-IS",
    "it-CH",
    "it-IT",
    "iw-IL",
    "ja-JP",
    "jv-ID",
    "ka-GE",
    "kam-KE",
    "kea-CV",
    "kk-KZ",
    "km-KH",
    "kn-IN",
    "ko-KR",
    "ky-KG",
    "lb-LU",
    "lg-UG",
    "ln-CD",
    "lo-LA",
    "lt-LT",
    "luo-KE",
    "lv-LV",
    "mi-NZ",
    "mk-MK",
    "ml-IN",
    "mn-MN",
    "mr-IN",
    "ms-MY",
    "mt-MT",
    "my-MM",
    "ne-NP",
    "nl-BE",
    "nl-NL",
    "no-NO",
    "nso-ZA",
    "ny-MW",
    "oc-FR",
    "om-ET",
    "or-IN",
    "pa-Guru-IN",
    "pl-PL",
    "ps-AF",
    "pt-BR",
    "pt-PT",
    "ro-RO",
    "ru-RU",
    "rup-BG",
    "rw-RW",
    "sd-IN",
    "si-LK",
    "sk-SK",
    "sl-SI",
    "sn-ZW",
    "so-SO",
    "sq-AL",
    "sr-RS",
    "ss-Latn-ZA",
    "st-ZA",
    "su-ID",
    "sv-SE",
    "sw",
    "sw-KE",
    "ta-IN",
    "te-IN",
    "tg-TJ",
    "th-TH",
    "tn-Latn-ZA",
    "tr-TR",
    "ts-ZA",
    "uk-UA",
    "umb-AO",
    "ur-PK",
    "uz-UZ",
    "ve-ZA",
    "vi-VN",
    "wo-SN",
    "xh-ZA",
    "yo-NG",
    "yue-Hant-HK",
    "zu-ZA",
]

Gender = Literal["male", "female", "neutral"]

ChatModels = Literal[
    "gemini-2.5-pro-preview-05-06",
    "gemini-2.5-flash-preview-04-17",
    "gemini-2.5-flash-preview-05-20",
    "gemini-2.0-flash-001",
    "gemini-2.0-flash-lite-preview-02-05",
    "gemini-2.0-pro-exp-02-05",
    "gemini-1.5-pro",
]

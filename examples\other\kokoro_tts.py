import logging

from dotenv import load_dotenv

from livekit.agents import Agent, AgentSession, JobContext, JobProcess, WorkerOptions, cli, metrics
from livekit.agents.voice import MetricsCollectedEvent
from livekit.plugins import deepgram, openai, silero

logger = logging.getLogger("kokoro-tts-agent")

load_dotenv()

# This example demonstrates how to use the Kokoro TTS model with LiveKit Agents
# with OpenAI-compatible endpoint of Kokoro-FastAPI https://github.com/remsky/Kokoro-FastAPI
# Kokoro-FastAPI can run locally on CPU or GPU devices with docker under linux and MacOS


class MyAgent(Agent):
    def __init__(self) -> None:
        super().__init__(
            instructions="Your name is <PERSON>. You would interact with users via voice."
            "with that in mind keep your responses concise and to the point."
            "You are curious and friendly, and have a sense of humor.",
        )


def prewarm(proc: JobProcess):
    proc.userdata["vad"] = silero.VAD.load()


async def entrypoint(ctx: JobContext):
    # each log entry will include these fields
    ctx.log_context_fields = {
        "room": ctx.room.name,
        "user_id": "your user_id",
    }
    session = AgentSession(
        vad=ctx.proc.userdata["vad"],
        # any combination of STT, LLM, TTS, or realtime API can be used
        llm=openai.LLM(model="gpt-4o-mini"),
        stt=deepgram.STT(model="nova-3", language="multi"),
        tts=openai.TTS(
            model="kokoro",
            voice="af_alloy",
            api_key="not-needed",
            base_url="http://localhost:8880/v1",
            response_format="wav",
        ),
    )

    @session.on("metrics_collected")
    def _on_metrics_collected(ev: MetricsCollectedEvent):
        metrics.log_metrics(ev.metrics)

    await session.start(agent=MyAgent(), room=ctx.room)


if __name__ == "__main__":
    cli.run_app(WorkerOptions(entrypoint_fnc=entrypoint, prewarm_fnc=prewarm))

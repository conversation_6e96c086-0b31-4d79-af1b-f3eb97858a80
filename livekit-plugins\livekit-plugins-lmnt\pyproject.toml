[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "livekit-plugins-lmnt"
dynamic = ["version"]
description = "LMNT TTS plugin for LiveKit agents"
readme = "README.md"
license = "Apache-2.0"
requires-python = ">=3.9.0"
authors = [
    {name = "LiveKit", email = "<EMAIL>"}
]
keywords = ["webrtc", "realtime", "audio", "livekit", "LMNT", "TTS", "voice"]
classifiers = [
    "Intended Audience :: Developers",
    "License :: OSI Approved :: Apache Software License",
    "Topic :: Multimedia :: Sound/Audio",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3 :: Only",
]
dependencies = ["livekit-agents>=1.2.6"]

[project.urls]
Documentation = "https://docs.livekit.io"
Website = "https://livekit.io/"
Source = "https://github.com/livekit/agents"

[tool.hatch.version]
path = "livekit/plugins/lmnt/version.py"

[tool.hatch.build.targets.wheel]
packages = ["livekit"]

[tool.hatch.build.targets.sdist]
include = ["/livekit"]

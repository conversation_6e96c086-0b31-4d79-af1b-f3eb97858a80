# Uplift AI plugin for LiveKit Agents

Support for voice synthesis with [Uplift AI](https://upliftai.org) for underserved languages.

See [https://docs.upliftai.org/orator_voices](https://docs.upliftai.org/orator_voices) for supported voices and languages.

## Installation

```bash
pip install livekit-plugins-upliftai
```

## Pre-requisites

You'll need an API key from Uplift AI. It can be set as an environment variable: `UPLIFTAI_API_KEY`. You can get your API key by signing up at [https://upliftai.org](https://upliftai.org).


## Tutorial

Follow along at [https://docs.upliftai.org/tutorials/livekit-voice-agent](https://docs.upliftai.org/tutorials/livekit-voice-agent) where we build a voice agent using LiveKit and Uplift AI.
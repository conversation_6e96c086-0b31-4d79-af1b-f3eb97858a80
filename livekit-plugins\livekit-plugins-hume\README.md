# Hume AI TTS plugin for LiveKit Agents

Support for text-to-speech with [<PERSON>](https://www.hume.ai/).

See [https://docs.livekit.io/agents/integrations/tts/hume/](https://docs.livekit.io/agents/integrations/tts/hume/) for more information.

## Installation

```bash
pip install livekit-plugins-hume
```

You will need an API Key from Hume, it can be set as an environment variable: `HUME_API_KEY`. You can get it from [here](https://platform.hume.ai/settings/keys)

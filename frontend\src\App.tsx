"use client"

import React, { useState, useRef, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Progress } from '@/components/ui/progress'
import { 
  <PERSON>, 
  <PERSON>tings, 
  RotateCcw, 
  <PERSON><PERSON>, 
  <PERSON><PERSON><PERSON><PERSON>, 
  Volume2, 
  VolumeX, 
  Phone, 
  Bot, 
  Circle, 
  Clock, 
  Cpu, 
  Cloud, 
  Mail, 
  Search, 
  Download,
  Trash2,
  Sun,
  Moon,
  Zap
} from 'lucide-react'

interface Message {
  id: string
  type: 'user' | 'agent'
  content: string
  timestamp: Date
}

interface AgentStatus {
  online: boolean
  name: string
  model: string
  latency: number
}

interface ToolExecution {
  id: string
  name: string
  timestamp: Date
  result: string
}

const LiveKitAgentInterface: React.FC = () => {
  const [messages, setMessages] = useState<Message[]>([
    {
      id: '1',
      type: 'agent',
      content: 'Hi! I\'m Kelly, your AI assistant. How can I help you today?',
      timestamp: new Date(Date.now() - 60000)
    }
  ])
  const [inputValue, setInputValue] = useState('')
  const [isTyping, setIsTyping] = useState(false)
  const [micEnabled, setMicEnabled] = useState(false)
  const [speakerEnabled, setSpeakerEnabled] = useState(true)
  const [isDarkMode, setIsDarkMode] = useState(false)
  const [selectedModel, setSelectedModel] = useState('gpt-4')
  const [agentInstructions, setAgentInstructions] = useState('You are Kelly, a helpful AI assistant. Be friendly and concise in your responses.')
  const [micLevel, setMicLevel] = useState(0)
  const [speakerLevel, setSpeakerLevel] = useState(0)
  
  const messagesEndRef = useRef<HTMLDivElement>(null)
  
  const agentStatus: AgentStatus = {
    online: true,
    name: 'Kelly',
    model: selectedModel,
    latency: 1.2
  }

  const availableTools = [
    { name: 'Weather', icon: Cloud, description: 'Get weather information' },
    { name: 'Email', icon: Mail, description: 'Send emails' },
    { name: 'Search', icon: Search, description: 'Web search' }
  ]

  const toolExecutions: ToolExecution[] = [
    {
      id: '1',
      name: 'Weather',
      timestamp: new Date(Date.now() - 30000),
      result: 'Retrieved weather for San Francisco: 70°F, Sunny'
    }
  ]

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' })
  }

  useEffect(() => {
    scrollToBottom()
  }, [messages])

  useEffect(() => {
    const interval = setInterval(() => {
      setMicLevel(micEnabled ? Math.random() * 100 : 0)
      setSpeakerLevel(Math.random() * 60)
    }, 100)
    return () => clearInterval(interval)
  }, [micEnabled])

  const handleSendMessage = () => {
    if (!inputValue.trim()) return

    const newMessage: Message = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue,
      timestamp: new Date()
    }

    setMessages(prev => [...prev, newMessage])
    setInputValue('')
    setIsTyping(true)

    setTimeout(() => {
      const agentResponse: Message = {
        id: (Date.now() + 1).toString(),
        type: 'agent',
        content: `I understand you said: "${inputValue}". Let me help you with that!`,
        timestamp: new Date()
      }
      setMessages(prev => [...prev, agentResponse])
      setIsTyping(false)
    }, 2000)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      handleSendMessage()
    }
  }

  const clearSession = () => {
    setMessages([{
      id: Date.now().toString(),
      type: 'agent',
      content: 'Hi! I\'m Kelly, your AI assistant. How can I help you today?',
      timestamp: new Date()
    }])
  }

  const exportChat = () => {
    const chatData = JSON.stringify(messages, null, 2)
    const blob = new Blob([chatData], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'chat-export.json'
    a.click()
  }

  return (
    <div className={`min-h-screen ${isDarkMode ? 'dark' : ''}`}>
      <div className="bg-background text-foreground min-h-screen">
        {/* Header */}
        <div className="border-b border-border p-4">
          <div className="flex items-center justify-between max-w-7xl mx-auto">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Bot className="h-6 w-6 text-primary" />
                <span className="font-semibold text-lg">LiveKit Agent</span>
              </div>
              <Badge variant={agentStatus.online ? "default" : "secondary"} className="gap-1">
                <Circle className={`h-2 w-2 fill-current ${agentStatus.online ? 'text-green-500' : 'text-gray-500'}`} />
                {agentStatus.online ? 'Online' : 'Offline'}
              </Badge>
            </div>

            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm" onClick={() => setIsDarkMode(!isDarkMode)}>
                {isDarkMode ? <Sun className="h-4 w-4" /> : <Moon className="h-4 w-4" />}
              </Button>
              <Button variant="outline" size="sm" onClick={clearSession}>
                <RotateCcw className="h-4 w-4 mr-2" />
                New Session
              </Button>
              <Sheet>
                <SheetTrigger asChild>
                  <Button variant="outline" size="sm">
                    <Settings className="h-4 w-4 mr-2" />
                    Settings
                  </Button>
                </SheetTrigger>
                <SheetContent>
                  <SheetHeader>
                    <SheetTitle>Agent Settings</SheetTitle>
                    <SheetDescription>Configure your AI agent preferences</SheetDescription>
                  </SheetHeader>

                  <Tabs defaultValue="model" className="mt-6">
                    <TabsList className="grid w-full grid-cols-3">
                      <TabsTrigger value="model">Model</TabsTrigger>
                      <TabsTrigger value="voice">Voice</TabsTrigger>
                      <TabsTrigger value="advanced">Advanced</TabsTrigger>
                    </TabsList>

                    <TabsContent value="model" className="space-y-4">
                      <div>
                        <label className="text-sm font-medium">LLM Model</label>
                        <Select value={selectedModel} onValueChange={setSelectedModel}>
                          <SelectTrigger className="mt-1">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="gpt-4">GPT-4</SelectItem>
                            <SelectItem value="gpt-3.5-turbo">GPT-3.5 Turbo</SelectItem>
                            <SelectItem value="claude-3">Claude 3</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <label className="text-sm font-medium">Agent Instructions</label>
                        <Textarea
                          value={agentInstructions}
                          onChange={(e) => setAgentInstructions(e.target.value)}
                          className="mt-1"
                          rows={4}
                        />
                      </div>
                    </TabsContent>

                    <TabsContent value="voice" className="space-y-4">
                      <div>
                        <label className="text-sm font-medium">TTS Voice</label>
                        <Select defaultValue="kelly">
                          <SelectTrigger className="mt-1">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="kelly">Kelly (Female)</SelectItem>
                            <SelectItem value="alex">Alex (Male)</SelectItem>
                            <SelectItem value="sam">Sam (Neutral)</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <label className="text-sm font-medium">STT Language</label>
                        <Select defaultValue="en-US">
                          <SelectTrigger className="mt-1">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="en-US">English (US)</SelectItem>
                            <SelectItem value="en-GB">English (UK)</SelectItem>
                            <SelectItem value="es-ES">Spanish</SelectItem>
                            <SelectItem value="fr-FR">French</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </TabsContent>

                    <TabsContent value="advanced" className="space-y-4">
                      <div className="space-y-2">
                        <Button variant="outline" className="w-full justify-start" onClick={exportChat}>
                          <Download className="h-4 w-4 mr-2" />
                          Export Chat History
                        </Button>
                        <Button variant="outline" className="w-full justify-start" onClick={clearSession}>
                          <Trash2 className="h-4 w-4 mr-2" />
                          Clear All History
                        </Button>
                      </div>
                    </TabsContent>
                  </Tabs>
                </SheetContent>
              </Sheet>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="max-w-7xl mx-auto p-4 grid grid-cols-1 lg:grid-cols-4 gap-4 h-[calc(100vh-80px)]">
          {/* Chat Interface */}
          <div className="lg:col-span-3 flex flex-col">
            <Card className="flex-1 flex flex-col">
              <CardHeader className="pb-3">
                <CardTitle className="text-lg">Chat with Kelly</CardTitle>
              </CardHeader>
              <CardContent className="flex-1 flex flex-col p-0">
                <ScrollArea className="flex-1 px-6">
                  <div className="space-y-4 pb-4">
                    {messages.map((message) => (
                      <div key={message.id} className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}>
                        <div className={`max-w-[80%] rounded-lg p-3 ${
                          message.type === 'user'
                            ? 'bg-primary text-primary-foreground'
                            : 'bg-muted'
                        }`}>
                          <div className="text-sm">{message.content}</div>
                          <div className="text-xs opacity-70 mt-1">
                            {message.timestamp.toLocaleTimeString()}
                          </div>
                        </div>
                      </div>
                    ))}

                    {isTyping && (
                      <div className="flex justify-start">
                        <div className="bg-muted rounded-lg p-3">
                          <div className="flex items-center gap-1">
                            <div className="flex gap-1">
                              <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                              <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                              <div className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
                            </div>
                            <span className="text-xs ml-2">Kelly is typing...</span>
                          </div>
                        </div>
                      </div>
                    )}
                    <div ref={messagesEndRef} />
                  </div>
                </ScrollArea>

                <div className="p-6 pt-0">
                  <div className="flex gap-2">
                    <Input
                      value={inputValue}
                      onChange={(e) => setInputValue(e.target.value)}
                      onKeyPress={handleKeyPress}
                      placeholder="Type your message..."
                      className="flex-1"
                    />
                    <Button onClick={handleSendMessage} disabled={!inputValue.trim()}>
                      <Send className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Side Panel */}
          <div className="space-y-4">
            {/* Agent Status */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Agent Status</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center gap-2">
                  <Bot className="h-5 w-5 text-primary" />
                  <span className="font-medium">{agentStatus.name}</span>
                </div>

                <div className="space-y-2 text-sm">
                  <div className="flex items-center justify-between">
                    <span className="text-muted-foreground">Model:</span>
                    <Badge variant="outline" className="text-xs">
                      <Cpu className="h-3 w-3 mr-1" />
                      {agentStatus.model}
                    </Badge>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-muted-foreground">Latency:</span>
                    <Badge variant="outline" className="text-xs">
                      <Clock className="h-3 w-3 mr-1" />
                      {agentStatus.latency}s
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Audio Controls */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Audio Controls</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Microphone</span>
                  <Button
                    variant={micEnabled ? "default" : "outline"}
                    size="sm"
                    onClick={() => setMicEnabled(!micEnabled)}
                  >
                    {micEnabled ? <Mic className="h-4 w-4" /> : <MicOff className="h-4 w-4" />}
                  </Button>
                </div>

                {micEnabled && (
                  <div className="space-y-1">
                    <div className="text-xs text-muted-foreground">Input Level</div>
                    <Progress value={micLevel} className="h-2" />
                  </div>
                )}

                <div className="flex items-center justify-between">
                  <span className="text-sm">Speaker</span>
                  <Button
                    variant={speakerEnabled ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSpeakerEnabled(!speakerEnabled)}
                  >
                    {speakerEnabled ? <Volume2 className="h-4 w-4" /> : <VolumeX className="h-4 w-4" />}
                  </Button>
                </div>

                <div className="space-y-1">
                  <div className="text-xs text-muted-foreground">Output Level</div>
                  <Progress value={speakerLevel} className="h-2" />
                </div>

                <Button variant="outline" className="w-full">
                  <Phone className="h-4 w-4 mr-2" />
                  Push to Talk
                </Button>
              </CardContent>
            </Card>

            {/* Available Tools */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">Available Tools</CardTitle>
              </CardHeader>
              <CardContent className="space-y-2">
                {availableTools.map((tool) => (
                  <div key={tool.name} className="flex items-center gap-2 p-2 rounded-md hover:bg-muted/50 cursor-pointer">
                    <tool.icon className="h-4 w-4 text-primary" />
                    <div className="flex-1">
                      <div className="text-sm font-medium">{tool.name}</div>
                      <div className="text-xs text-muted-foreground">{tool.description}</div>
                    </div>
                  </div>
                ))}
              </CardContent>
            </Card>

            {/* Tool Execution Log */}
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center gap-2">
                  <Zap className="h-4 w-4" />
                  Recent Tool Calls
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-24">
                  <div className="space-y-2">
                    {toolExecutions.map((execution) => (
                      <div key={execution.id} className="text-xs">
                        <div className="font-medium">{execution.name}</div>
                        <div className="text-muted-foreground">{execution.result}</div>
                        <div className="text-muted-foreground">{execution.timestamp.toLocaleTimeString()}</div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}

export default LiveKitAgentInterface

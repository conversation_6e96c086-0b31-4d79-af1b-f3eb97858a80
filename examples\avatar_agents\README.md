# Avatar Agents Examples

This directory contains examples demonstrating how to integrate visual avatars with voice agents using various avatar providers. These examples show how to create engaging, face-to-face interactions by combining LiveKit's voice agent capabilities with animated avatar representations.

## 🏗️ Avatar Provider Categories

### ☁️ Cloud-Based with Custom Avatar ID

These providers work with pre-configured avatars using unique avatar identifiers:

- **[Anam](./anam/)** - [Platform](https://anam.ai/) | [Integration Guide](https://docs.anam.ai/third-party-integrations/livekit)
- **[Beyond Presence (Bey)](./bey/)** - [Platform](https://bey.dev/) | [Integration Guide](https://docs.bey.dev/integration/livekit)
- **[BitHuman](./bithuman/)** (Cloud mode) - [Platform](https://bithuman.ai/) | [Integration Guide](https://sdk.docs.bithuman.ai/#/preview/livekit-cloud-plugin)
- **[Simli](./simli/)** - [Platform](https://app.simli.com/)
- **[Tavus](./tavus/)** - [Platform](https://www.tavus.io/)

### 🖼️ Cloud-Based with Image Upload

These providers allow direct image upload to create custom avatars:

- **[Hedra](./hedra/)** - [Platform](https://www.hedra.com/)
- **[BitHuman](./bithuman/)** (Cloud mode) - [Platform](https://bithuman.ai/) | [Integration Guide](https://sdk.docs.bithuman.ai/#/preview/livekit-cloud-plugin)

### 💻 Local Processing

These solutions run locally without cloud dependencies:

- **[BitHuman](./bithuman/)** (Local mode) - [Platform](https://bithuman.ai/) | [Integration Guide](https://sdk.docs.bithuman.ai/#/preview/livekit-cloud-plugin)
- **[Audio Wave Visualization](./audio_wave/)** - Mock avatar for development and testing


## 📖 Additional Resources

- [LiveKit Avatar Integration Documentation](https://docs.livekit.io/agents/integrations/avatar/)
- [LiveKit Agents Documentation](https://docs.livekit.io/agents/)

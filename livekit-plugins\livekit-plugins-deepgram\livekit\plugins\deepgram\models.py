from typing import Literal

DeepgramModels = Literal[
    "nova-general",
    "nova-phonecall",
    "nova-meeting",
    "nova-2-general",
    "nova-2-meeting",
    "nova-2-phonecall",
    "nova-2-finance",
    "nova-2-conversationalai",
    "nova-2-voicemail",
    "nova-2-video",
    "nova-2-medical",
    "nova-2-drivethru",
    "nova-2-automotive",
    "nova-3",
    "nova-3-general",
    "nova-3-medical",
    "enhanced-general",
    "enhanced-meeting",
    "enhanced-phonecall",
    "enhanced-finance",
    "base",
    "meeting",
    "phonecall",
    "finance",
    "conversationalai",
    "voicemail",
    "video",
    "whisper-tiny",
    "whisper-base",
    "whisper-small",
    "whisper-medium",
    "whisper-large",
]

DeepgramLanguages = Literal[
    "zh",
    "zh-CN",
    "zh-TW",
    "da",
    "nl",
    "en",
    "en-US",
    "en-AU",
    "en-GB",
    "en-NZ",
    "en-IN",
    "fr",
    "fr-CA",
    "de",
    "hi",
    "hi-Latn",
    "pt",
    "pt-BR",
    "es",
    "es-419",
    "hi",
    "hi-Latn",
    "it",
    "ja",
    "ko",
    "no",
    "pl",
    "pt",
    "pt-BR",
    "es-LATAM",
    "sv",
    "ta",
    "taq",
    "uk",
    "tr",
    "sv",
    "id",
    "pt",
    "pt-BR",
    "ru",
    "th",
    "multi",
]

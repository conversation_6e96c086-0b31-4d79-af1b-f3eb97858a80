from typing import Literal

GladiaModels = Literal["base"]

GladiaLanguages = Literal[
    "af",
    "sq",
    "am",
    "ar",
    "hy",
    "as",
    "ast",
    "az",
    "ba",
    "eu",
    "be",
    "bn",
    "bs",
    "br",
    "bg",
    "my",
    "es",
    "ca",
    "ceb",
    "zh",
    "hr",
    "cs",
    "da",
    "nl",
    "en",
    "et",
    "fo",
    "fi",
    "fr",
    "fy",
    "ff",
    "gd",
    "gl",
    "lg",
    "ka",
    "de",
    "el",
    "gu",
    "ht",
    "ha",
    "haw",
    "he",
    "hi",
    "hu",
    "is",
    "ig",
    "ilo",
    "id",
    "ga",
    "it",
    "ja",
    "jv",
    "kn",
    "kk",
    "km",
    "ko",
    "lo",
    "la",
    "lv",
    "lb",
    "ln",
    "lt",
    "mk",
    "mg",
    "ms",
    "ml",
    "mt",
    "mi",
    "mr",
    "mo",
    "mn",
    "ne",
    "no",
    "nn",
    "oc",
    "or",
    "pa",
    "ps",
    "fa",
    "pl",
    "pt",
    "ro",
    "ru",
    "sa",
    "sr",
    "sn",
    "sd",
    "si",
    "sk",
    "sl",
    "so",
    "su",
    "sw",
    "ss",
    "sv",
    "tl",
    "tg",
    "ta",
    "tt",
    "te",
    "th",
    "bo",
    "tn",
    "tr",
    "tk",
    "uk",
    "ur",
    "uz",
    "vi",
    "cy",
    "wo",
    "xh",
    "yi",
    "yo",
    "zu",
]

name: CI

on:
  push:
    branches: [main, 0.x]
  pull_request:
    branches: [main, 0.x]
  workflow_dispatch:

jobs:
  ruff:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - name: Install uv
        uses: astral-sh/setup-uv@v5
        with:
          enable-cache: true
          cache-dependency-glob: "uv.lock"

      - uses: actions/setup-python@v5
        with:
          python-version: "3.9"

      - name: Install the project
        run: uv sync --all-extras --dev

      - name: Ruff
        run: uv run ruff check --output-format=github .

      - name: Check format
        run: uv run ruff format --check .

  type-check:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.9", "3.10"]
        
    steps:
      - uses: actions/checkout@v4
      - name: Install uv
        uses: astral-sh/setup-uv@v5
        with:
          enable-cache: true
          cache-dependency-glob: "uv.lock"

      - uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}

      - name: Install the project
        run: uv sync --all-extras --dev

      - name: Install pip
        # mypy --install-types requires pip
        run: uv pip install pip

      - name: Install types-requests for Python < 3.10 (conflicts with botocore)
        if: matrix.python-version == '3.9'
        run: uv pip install "types-requests<********"

      - name: Check Types
        run: uv run mypy --install-types --non-interactive
              -p livekit.agents
              -p livekit.plugins.openai
              -p livekit.plugins.anthropic
              -p livekit.plugins.mistralai
              -p livekit.plugins.assemblyai
              -p livekit.plugins.aws
              -p livekit.plugins.azure
              -p livekit.plugins.bey
              -p livekit.plugins.bithuman
              -p livekit.plugins.cartesia
              -p livekit.plugins.clova
              -p livekit.plugins.deepgram
              -p livekit.plugins.elevenlabs
              -p livekit.plugins.fal
              -p livekit.plugins.gladia
              -p livekit.plugins.google
              -p livekit.plugins.groq
              -p livekit.plugins.hume
              -p livekit.plugins.minimal
              -p livekit.plugins.neuphonic
              -p livekit.plugins.nltk
              -p livekit.plugins.playai
              -p livekit.plugins.resemble
              -p livekit.plugins.rime
              -p livekit.plugins.silero
              -p livekit.plugins.speechify
              -p livekit.plugins.speechmatics
              -p livekit.plugins.tavus
              -p livekit.plugins.turn_detector
              -p livekit.plugins.hedra
              -p livekit.plugins.langchain
              -p livekit.plugins.baseten
              -p livekit.plugins.sarvam
              -p livekit.plugins.inworld
              -p livekit.plugins.simli
              -p livekit.plugins.anam

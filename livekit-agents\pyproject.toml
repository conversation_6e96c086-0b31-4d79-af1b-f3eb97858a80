[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "livekit-agents"
dynamic = ["version"]
description = "A powerful framework for building realtime voice AI agents"
readme = "README.md"
license = "Apache-2.0"
requires-python = ">=3.9"
authors = [{ name = "LiveKit", email = "<EMAIL>" }]
keywords = ["webrtc", "realtime", "audio", "video", "livekit", "agents", "AI"]
classifiers = [
    "Intended Audience :: Developers",
    "License :: OSI Approved :: Apache Software License",
    "Topic :: Multimedia :: Sound/Audio",
    "Topic :: Multimedia :: Video",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "Programming Language :: Python :: 3 :: Only",
]
dependencies = [
    "click~=8.1",
    "livekit>=1.0.12,<2",
    "livekit-api>=1.0.5,<2",
    "livekit-protocol~=1.0",
    "livekit-blingfire~=1.0",
    "protobuf>=3",
    "pyjwt>=2.0",
    "types-protobuf>=4",
    "watchfiles>=1.0",
    "psutil>=7.0",
    "aiohttp~=3.10",
    "typing-extensions>=4.12",
    "sounddevice>=0.5",
    "docstring_parser>=0.16",
    "eval-type-backport",
    "colorama>=0.4.6",
    "av>=14.0.0",
    "numpy>=1.26.0",
    "pydantic>=2.0,<3",
    "nest-asyncio>=1.6.0",
    "opentelemetry-api>=1.34",
    "opentelemetry-sdk>=1.34.1",
    "opentelemetry-exporter-otlp>=1.34.1",
    "prometheus-client>=0.22",
]

[project.optional-dependencies]
mcp = ["mcp>=1.10.0, <2; python_version >= '3.10'"]
codecs = ["av>=12.0.0", "numpy>=1.26.0"]
images = ["pillow>=10.3.0"]
anam = ["livekit-plugins-anam>=1.2.6"]
anthropic = ["livekit-plugins-anthropic>=1.2.6"]
assemblyai = ["livekit-plugins-assemblyai>=1.2.6"]
aws = ["livekit-plugins-aws>=1.2.6"]
azure = ["livekit-plugins-azure>=1.2.6"]
baseten = ["livekit-plugins-baseten>=1.2.6"]
bey = ["livekit-plugins-bey>=1.2.6"]
bithuman = ["livekit-plugins-bithuman>=1.2.6"]
cartesia = ["livekit-plugins-cartesia>=1.2.6"]
clova = ["livekit-plugins-clova>=1.2.6"]
deepgram = ["livekit-plugins-deepgram>=1.2.6"]
elevenlabs = ["livekit-plugins-elevenlabs>=1.2.6"]
fal = ["livekit-plugins-fal>=1.2.6"]
gladia = ["livekit-plugins-gladia>=1.2.6"]
google = ["livekit-plugins-google>=1.2.6"]
groq = ["livekit-plugins-groq>=1.2.6"]
hedra = ["livekit-plugins-hedra>=1.2.6"]
hume = ["livekit-plugins-hume>=1.2.6"]
inworld = ["livekit-plugins-inworld>=1.2.6"]
langchain = ["livekit-plugins-langchain>=1.2.6"]
lmnt = ["livekit-plugins-lmnt>=1.2.6"]
mistralai = ["livekit-plugins-mistralai>=1.2.6"]
neuphonic = ["livekit-plugins-neuphonic>=1.2.6"]
nltk = ["livekit-plugins-nltk>=1.2.6"]
openai = ["livekit-plugins-openai>=1.2.6"]
playai = ["livekit-plugins-playai>=1.2.6"]
resemble = ["livekit-plugins-resemble>=1.2.6"]
rime = ["livekit-plugins-rime>=1.2.6"]
sarvam = ["livekit-plugins-sarvam>=1.2.6"]
silero = ["livekit-plugins-silero>=1.2.6"]
simli = ["livekit-plugins-simli>=1.2.6"]
smallestai = ["livekit-plugins-smallestai>=1.2.6"]
speechify = ["livekit-plugins-speechify>=1.2.6"]
speechmatics = ["livekit-plugins-speechmatics>=1.2.6"]
spitch = ["livekit-plugins-spitch>=1.2.6"]
tavus = ["livekit-plugins-tavus>=1.2.6"]
turn-detector = ["livekit-plugins-turn-detector>=1.2.6"]
upliftai = ["livekit-plugins-upliftai>=1.2.6"]

[project.urls]
Documentation = "https://docs.livekit.io"
Website = "https://livekit.io/"
Source = "https://github.com/livekit/agents"

[tool.hatch.version]
path = "livekit/agents/version.py"

[tool.hatch.build.targets.wheel]
packages = ["livekit"]
include = ["livekit/agents/resources/*", "livekit/agents/debug/index.html"]


[tool.hatch.build.targets.sdist]
include = ["/livekit"]

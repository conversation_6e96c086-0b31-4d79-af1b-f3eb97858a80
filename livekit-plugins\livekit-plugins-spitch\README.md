# Spitch plugin for LiveKit Agents

Support for [Spitch](https://spitch.app/)'s African-language voice AI services in LiveKit Agents.

More information is available in the docs for the [STT](https://docs.livekit.io/agents/integrations/stt/spitch/) and [TTS](https://docs.livekit.io/agents/integrations/tts/spitch/) integrations.

## Installation

```bash
pip install livekit-plugins-spitch
```

## Pre-requisites

You'll need an API key from Spitch. It can be set as an environment variable: `SPITCH_API_KEY`

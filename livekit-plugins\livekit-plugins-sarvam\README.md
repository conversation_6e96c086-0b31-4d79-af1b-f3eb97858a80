# Sarvam.ai Plugin for LiveKit Agents

Support for [Sarvam.ai](https://sarvam.ai)'s Indian-language voice AI services in LiveKit Agents.

## Features

- **Speech-to-Text (STT)**: Convert audio to text using Sarvam's "Saarika" models. See the [STT docs](https://docs.livekit.io/agents/integrations/stt/sarvam/) for more information.
- **Text-to-Speech (TTS)**: Convert text to audio using Sarvam's "Bulbul" models. See the [TTS docs](https://docs.livekit.io/agents/integrations/tts/sarvam/) for more information.

## Installation 

```bash
pip install livekit-plugins-sarvam
```

## Pre-requisites

You'll need an API key from Sarvam.ai. It can be set as an environment variable: `SARVAM_API_KEY` 